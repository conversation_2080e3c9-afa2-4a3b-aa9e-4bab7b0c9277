<template>
  <div class="energy-consumption-stats">
    <div class="consumption-grid">
      <div class="consumption-item" v-for="(item, index) in consumptionItems" :key="index">
        <div class="consumption-value">{{ item.value }}</div>
        <div class="consumption-label">{{ item.label }}</div>
        <div class="consumption-unit">{{ item.unit }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  timeDimension: {
    type: String,
    default: 'day'
  }
})

// 不同时间维度的数据
const consumptionData = {
  day: [
    { value: '31.0', label: '本日', unit: '单位：kWh' }
  ],
  month: [
    { value: '283.0', label: '本月', unit: '单位：kWh' }
  ],
  year: [
    { value: '283.0', label: '本年', unit: '单位：kWh' }
  ]
}

// 始终显示所有三个维度的数据
const consumptionItems = computed(() => {
  return [
    { value: '31.0', label: '本日', unit: '单位：kWh' },
    { value: '283.0', label: '本月', unit: '单位：kWh' },
    { value: '283.0', label: '本年', unit: '单位：kWh' }
  ]
})
</script>

<style lang="less" scoped>
.energy-consumption-stats {
  .consumption-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 10px 0;

    .consumption-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(216, 216, 216, 0.05);
      border-radius: 8px;
      padding: 20px 16px;
      min-height: 120px;
      border: 1px solid;
      border-image: linear-gradient(
        145deg,
        rgba(255, 255, 255, 0.2) 2%,
        rgba(255, 255, 255, 0) 34%,
        rgba(255, 255, 255, 0) 71%,
        rgba(255, 255, 255, 0.2) 101%
      ) 1 / 1 / 0 stretch;

      .consumption-value {
        font-family: D-DIN, Arial, sans-serif;
        font-size: 36px;
        font-weight: bold;
        color: #FFE5A8;
        margin-bottom: 8px;
        text-shadow: 0px 1px 16px rgba(255, 131, 0, 0.5);
        line-height: 1;
      }

      .consumption-label {
        font-family: SourceHanSans, Arial, sans-serif;
        font-size: 16px;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 6px;
        opacity: 0.9;
      }

      .consumption-unit {
        font-family: SourceHanSans, Arial, sans-serif;
        font-size: 12px;
        color: #ffffff;
        opacity: 0.7;
      }
    }
  }
}
</style>
