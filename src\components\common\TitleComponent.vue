<template>
    <div class="section-title">
        <!-- 折叠状态下只显示居中的按钮 -->
        <div v-if="collapsible && collapsed" class="collapsed-header">
            <div class="collapse-btn-center" @click="toggleCollapse">
                <svg-icon icon-class="xiangshang" class="collapse-icon" />
            </div>
        </div>

        <!-- 展开状态下显示完整标题 -->
        <div v-if="!collapsed" class="title">
            <div>{{ title }}</div>
            <div class="title-right">{{ subtitle }}</div>
            <slot name="extra">
                <!-- 时间维度切换 -->
                <div class="time-dimension" v-if="dimensions && dimensions.length">
                    <span v-for="item in dimensions" :key="item.value"
                        :class="['dimension-item', { active: modelValue === item.value }]"
                        @click="handleDimensionChange(item.value)">
                        {{ item.label }}
                    </span>
                </div>
            </slot>



            <!-- 展开/收起按钮 -->
            <div v-if="collapsible" class="collapse-btn" @click="toggleCollapse">
                <svg-icon icon-class="xiangxia" class="collapse-icon" />
            </div>
        </div>

        <div class="content" :class="{ 'content-collapsed': collapsed }">
            <transition name="slide">
                <div v-show="!collapsed" class="content-inner">
                    <slot></slot>
                </div>
            </transition>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import SvgIcon from '../icons/SvgIcon.vue'


const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    subtitle: {
        type: String,
        default: ''
    },
    modelValue: {
        type: String,
        default: 'month'
    },
    dimensions: {
        type: Array,
        default: () => []
    },
    showSelect: {
        type: Boolean,
        default: false
    },
    collapsible: {
        type: Boolean,
        default: false
    },
    defaultCollapsed: {
        type: Boolean,
        default: false
    },

});

// 下拉框数据和选中值
const selectValue = ref('A1')
const selectOptions = ref([
    { label: 'A1#', value: 'A1#' },
    { label: 'A2#', value: 'A2#' },
    { label: 'B1#', value: 'B1#' },
    { label: 'B2#', value: 'B2#' },
    { label: 'B3#', value: 'B3#' },
    { label: 'B4#', value: 'B4#' },
    { label: 'B5#', value: 'B5#' },
    { label: 'B6#', value: 'B6#' },
    { label: 'B7#', value: 'B7#' },
    { label: 'B8#', value: 'B8#' },
    { label: 'B9#', value: 'B9#' },
    { label: 'B10#', value: 'B10#' },
    { label: 'B11#', value: 'B11#' },
]);

const collapsed = ref(props.defaultCollapsed);
const emit = defineEmits(['update:modelValue', 'select-change', 'collapse-change', 'history-click']);

// 时间维度切换
const handleDimensionChange = (value) => {
    emit('update:modelValue', value);
};



// 下拉框选择
const handleSelectChange = (value) => {
    emit('select-change', value);
};

// 展开/收起切换
const toggleCollapse = () => {
    collapsed.value = !collapsed.value;
    emit('collapse-change', collapsed.value);
};
</script>

<style lang="less" scoped>
.section-title {
    .collapsed-header {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 40px;
    
        backdrop-filter: blur(10px);
        border-radius: 8px;
        margin-bottom: 0;

        .collapse-btn-center {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            transition: background-color 0.2s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .collapse-icon {
                width: 18px;
                height: 18px;
                color: #fff;
                opacity: 0.8;
                transition: opacity 0.2s ease;
            }

            &:hover .collapse-icon {
                opacity: 1;
            }
        }
    }

    .title {
        position: relative;
        z-index: 99;
        display: flex;
        align-items: center;
        width: 100%;
        height: 55px;
        background: linear-gradient(91deg, rgba(0, 103, 202, 0.63) 5%, rgba(0, 103, 202, 0) 120%), url('../../assets/images/header.png');
        background-size: 100% 100%;
        padding-left: 46px;


        font-family: PangMenZhengDao;
        font-size: 20px;

        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        font-weight: normal;
        font-size: 20px;
        // font-weight: bold;
        // background: linear-gradient(17deg, #FFFFFF 0%, #C3DFFF 100%);




        .title-right {
            opacity: 0.7;
            margin-left: 10px;
            font-family: Source Han Sans;
            font-size: 12px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 0em;

            font-variation-settings: "opsz" auto;
            background: linear-gradient(94deg, #FFFFFF 5%, #C3DFFF 109%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }



        .time-dimension {
            margin-left: auto;
            margin-right: 8px;
            display: flex;
            gap: 16px;

            .dimension-item {
                color: #ffffff;
                opacity: 0.6;
                cursor: pointer;

                transition: all 0.3s;
                font-family: PangMenZhengDao;
                font-size: 16px;
                font-weight: normal;
                line-height: normal;
                letter-spacing: 0em;

                &:hover {
                    opacity: 0.8;
                }

                &.active {
                    opacity: 1;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        bottom: -16px;
                        left: 0;
                        width: 100%;
                        height: 2px;
                        background: #0BFFD7;
                    }
                }
            }
        }

        .custom-select {
            margin-left: auto;
            margin-right: 20px;
            width: 75px;

            :deep(.el-input__wrapper) {
                background: rgba(255, 255, 255, 0.1);
                box-shadow: none;
                border: 1px solid rgba(255, 255, 255, 0.2);

                &:hover {
                    border-color: rgba(255, 255, 255, 0.3);
                }
            }

            :deep(.el-input__inner) {
                color: #fff;
                font-size: 14px;

                &::placeholder {
                    color: rgba(255, 255, 255, 0.6);
                }
            }
        }

        .collapse-btn {
            margin-right: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 4px;
            transition: background-color 0.2s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .collapse-icon {
                width: 16px;
                height: 16px;
                color: #fff;
                opacity: 0.8;
                transition: opacity 0.2s ease, transform 0.2s ease;
            }

            &:hover .collapse-icon {
                opacity: 1;
            }
        }
    }

    .content {
        background: rgba(6, 27, 47, 0.8);
        backdrop-filter: blur(14px);
        border-radius: 0 0 12px 12px;
      padding: 8px 16px;
        overflow: hidden;
        transition: all 0.3s ease;

        &.content-collapsed {
            padding: 0;
            border-radius: 0;
            background: transparent;
            backdrop-filter: none;
        }

        .content-inner {
            width: 100%;
        }
    }
}

// 下拉菜单样式
:deep(.el-select-dropdown) {
    background: rgba(6, 27, 47, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    .el-select-dropdown__item {
        color: #fff;

        &:hover,
        &.selected {
            background: rgba(255, 255, 255, 0.1);
        }
    }
}

// 滑动动画
.slide-enter-active,
.slide-leave-active {
    transition: all 0.3s ease;
    max-height: 500px; // 足够大的高度以容纳内容
    opacity: 1;
}

.slide-enter-from,
.slide-leave-to {
    max-height: 0;
    opacity: 0;
    margin-top: -16px;
}
</style>